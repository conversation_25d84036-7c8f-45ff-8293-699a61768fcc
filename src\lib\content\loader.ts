
import { getDynamicSeoContent } from './dynamic-content';

export async function loadToolContent(slug: string) {
  try {
    // أولاً، تحقق من وجود محتوى ديناميكي
    const dynamicContent = getDynamicSeoContent(slug);
    if (dynamicContent) {
      return dynamicContent;
    }

    switch (slug) {
      case 'summarize-arabic-text':
        return (await import('./tools/summarize-arabic-text')).default;
      case 'paraphrase-text':
        return (await import('./tools/paraphrase-text')).default;
      case 'hashtag-generator':
        return (await import('./tools/hashtag-generator')).default;
      case 'vat-calculator':
        return (await import('./tools/vat-calculator')).default;
      case 'currency-converter':
        return (await import('./tools/currency-converter')).default;
      case 'gold-price':
        return (await import('./tools/gold-price')).default;
      case 'silver-price':
        return (await import('./tools/silver-price')).default;
      case 'copper-price':
        return (await import('./tools/copper-price')).default;
      case 'aramco-stock':
        return (await import('./tools/aramco-stock')).default;
      case 'zakat-calculator':
        return (await import('./tools/zakat-calculator')).default;
      case 'discount-calculator':
        return (await import('./tools/discount-calculator')).default;
      case 'murabaha-calculator':
        return (await import('./tools/murabaha-calculator')).default;
      case 'cbm-calculator':
        return (await import('./tools/cbm-calculator')).default;
      case 'percentage-calculator':
        return (await import('./tools/percentage-calculator')).default;
      case 'hourly-wage-calculator':
        return (await import('./tools/hourly-wage-calculator')).default;
      case 'overtime-calculator':
        return (await import('./tools/overtime-calculator')).default;
      case 'retirement-calculator':
        return (await import('./tools/retirement-calculator')).default;
      case 'investment-calculator':
        return (await import('./tools/investment-calculator')).default;
      case 'qr-code-generator':
        return (await import('./tools/qr-code-generator')).default;
      case 'qr-code-reader':
        return (await import('./tools/qr-code-reader')).default;
      case 'number-to-words':
        return (await import('./tools/number-to-words')).default;
      case 'unit-converter':
        return (await import('./tools/unit-converter')).default;
      case 'text-repeater':
        return (await import('./tools/text-repeater')).default;
      case 'bmi-calculator':
        return (await import('./tools/bmi-calculator')).default;
      case 'calories-calculator':
        return (await import('./tools/calories-calculator')).default;
      case 'protein-calculator':
        return (await import('./tools/protein-calculator')).default;
      case 'macronutrient-calculator':
        return (await import('./tools/macronutrient-calculator')).default;
      case 'pregnancy-calculator':
        return (await import('./tools/pregnancy-calculator')).default;
      case 'ovulation-calculator':
        return (await import('./tools/ovulation-calculator')).default;
      case 'age-calculator':
        return (await import('./tools/age-calculator')).default;
      case 'date-difference':
        return (await import('./tools/date-difference')).default;
      case 'date-converter':
        return (await import('./tools/date-converter')).default;
      case 'todays-date':
        return (await import('./tools/todays-date')).default;
      case 'gpa-calculator':
        return (await import('./tools/gpa-calculator')).default;
      case 'weighted-grade-calculator':
        return (await import('./tools/weighted-grade-calculator')).default;
      case 'sample-size-calculator':
        return (await import('./tools/sample-size-calculator')).default;
      case 'jordanian-tawjihi-calculator':
        return (await import('./tools/jordanian-tawjihi-calculator')).default;
      case 'my-ip':
        return (await import('./tools/my-ip')).default;
      case 'whatsapp-tools':
        return (await import('./tools/whatsapp-tools')).default;
      case 'simple-calculator':
        return (await import('./tools/simple-calculator')).default;
      case 'sqrt-calculator':
        return (await import('./tools/sqrt-calculator')).default;
      case 'average-calculator':
        return (await import('./tools/average-calculator')).default;
      case 'reverse-text':
        return (await import('./tools/reverse-text')).default;
      case 'ounce-to-gram-converter':
        return (await import('./tools/ounce-to-gram-converter')).default;
      case 'word-count':
        return (await import('./tools/word-count')).default;
      case 'invisible-character':
        return (await import('./tools/invisible-character')).default;
      case 'mile-kilometer-converter':
        return (await import('./tools/mile-kilometer-converter')).default;
      case 'zodiac-sign-calculator':
        return (await import('./tools/zodiac-sign-calculator')).default;
      case 'age-difference-calculator':
        return (await import('./tools/age-difference-calculator')).default;
      case 'faddan-to-meter-converter':
        return (await import('./tools/faddan-to-meter-converter')).default;
      case 'gallon-to-liter-converter':
        return (await import('./tools/gallon-to-liter-converter')).default;
      case 'feet-to-meter-converter':
        return (await import('./tools/feet-to-meter-converter')).default;
      case 'pound-to-kg-converter':
        return (await import('./tools/pound-to-kg-converter')).default;
      case 'minutes-to-hours-converter':
        return (await import('./tools/minutes-to-hours-converter')).default;
      case 'hours-to-days-converter':
        return (await import('./tools/hours-to-days-converter')).default;
      case 'resignation-letter-generator':
        return (await import('./tools/resignation-letter-generator')).default;
      case 'financial-aid-request-generator':
        return (await import('./tools/financial-aid-request-generator')).default;
      case 'istikhara-prayer':
        return (await import('./tools/istikhara-prayer')).default;
      case 'multiplication-table':
        return (await import('./tools/multiplication-table')).default;
      case 'personality-strength-test':
        return (await import('./tools/personality-strength-test')).default;
      case 'jealousy-test':
        return (await import('./tools/jealousy-test')).default;
      case 'anger-test':
        return (await import('./tools/anger-test')).default;
      case 'love-test':
        return (await import('./tools/love-test')).default;
      case 'friendship-test':
        return (await import('./tools/friendship-test')).default;
      case 'masculinity-test':
        return (await import('./tools/masculinity-test')).default;
      case 'femininity-test':
        return (await import('./tools/femininity-test')).default;
      case 'spoiled-test':
        return (await import('./tools/spoiled-test')).default;
      case 'sensitivity-test':
        return (await import('./tools/sensitivity-test')).default;
      case 'spirit-animal-test':
        return (await import('./tools/spirit-animal-test')).default;
      case 'islamic-quiz':
        return (await import('./tools/islamic-quiz')).default;
      case 'islamic-marriage-test':
        return (await import('./tools/islamic-marriage-test')).default;
      case 'islamic-name-generator':
        return (await import('./tools/islamic-name-generator')).default;
      case 'color-blindness-test':
        return (await import('./tools/color-blindness-test')).default;
      case 'omani-dakhiliyah-dialect-test':
        return (await import('./tools/omani-dakhiliyah-dialect-test')).default;
      case 'kids-quiz':
        return (await import('./tools/kids-quiz')).default;
      case 'image-compressor':
        return (await import('./tools/image-compressor')).default;
      case 'jpg-to-png-converter':
        return (await import('./tools/jpg-to-png-converter')).default;
      case 'image-to-webp-converter':
        return (await import('./tools/image-to-webp-converter')).default;
      case 'png-to-jpg-converter':
        return (await import('./tools/png-to-jpg-converter')).default;
      case 'webp-to-png-converter':
        return (await import('./tools/webp-to-png-converter')).default;
      case 'background-removal':
        return (await import('./tools/background-removal')).default;
      case 'image-editor':
        return (await import('./tools/image-editor')).default;
      case 'event-cost-calculator':
        return (await import('./tools/event-cost-calculator')).default;
      case 'gift-suggester':
        return (await import('./tools/gift-suggester')).default;
      // Countdown tools now load their own content
      case 'saudi-salary-countdown':
        return (await import('./tools/salary-countdown')).default;
      case 'eid-alfitr-countdown':
        return (await import('./tools/eid-alfitr-countdown')).default;
      case 'eid-aladha-countdown':
        return (await import('./tools/eid-aladha-countdown')).default;
      case 'arafah-day-countdown':
        return (await import('./tools/arafah-day-countdown')).default;
      case 'founding-day-countdown':
        return (await import('./tools/founding-day-countdown')).default;
      case 'saudi-national-day-countdown':
        return (await import('./tools/saudi-national-day-countdown')).default;
      case 'citizen-account-countdown':
        return (await import('./tools/citizen-account-countdown')).default;
      case 'retirement-pension-countdown':
        return (await import('./tools/retirement-pension-countdown')).default;
      case 'housing-support-countdown':
        return (await import('./tools/housing-support-countdown')).default;
      case 'next-vacation-countdown':
        return (await import('./tools/next-vacation-countdown')).default;
      case 'winter-countdown':
        return (await import('./tools/winter-countdown')).default;
      case 'summer-end-countdown':
        return (await import('./tools/summer-end-countdown')).default;
      case 'study-calendar-countdown':
        return (await import('./tools/study-calendar-countdown')).default;
      case 'ramadan-countdown':
        return (await import('./tools/ramadan-countdown')).default;
      case 'pdf-to-images':
        return (await import('./tools/pdf-to-jpg')).default;
      case 'images-to-pdf':
        return (await import('./tools/images-to-pdf')).default;
       case 'split-pdf':
        return (await import('./tools/split-pdf')).default;
      case 'pdf-merger':
        return (await import('./tools/pdf-merger')).default;
      case 'pdf-compressor':
        return (await import('./tools/pdf-compressor')).default;
      case 'zatca-invoice-generator':
        return (await import('./tools/zatca-invoice-generator')).default;
      case 'zatca-qr-decoder':
        return (await import('./tools/zatca-qr-decoder')).default;
      default:
        // Generic fallback if a specific content file is not found
        try {
          return (await import(`./tools/${slug}`)).default;
        } catch (e) {
            return { seoDescription: null, faq: null };
        }
    }
  } catch (error) {
    console.warn(`Could not load content for tool slug: "${slug}"`, error);
    return { seoDescription: null, faq: null };
  }
}
